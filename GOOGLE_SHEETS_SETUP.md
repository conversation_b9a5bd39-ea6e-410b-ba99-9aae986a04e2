# Google Sheets Integration Setup Guide

This guide will help you set up Google Sheets integration for your DataGent waitlist form.

## Overview

The integration works by:
1. User submits the waitlist form on your website
2. Form data is sent to a Google Apps Script web app
3. Google Apps Script writes the data to a Google Sheet
4. If Google Sheets is unavailable, data falls back to localStorage

## Step-by-Step Setup

### 1. Create a Google Sheet

1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new blank spreadsheet
3. Name it "DataGent Waitlist" (or any name you prefer)
4. Copy the Sheet ID from the URL:
   - URL format: `https://docs.google.com/spreadsheets/d/SHEET_ID_HERE/edit`
   - Copy the `SHEET_ID_HERE` part

### 2. Set up Google Apps Script

1. Go to [Google Apps Script](https://script.google.com)
2. Click "New Project"
3. Replace the default code with the code from `google-apps-script/Code.gs`
4. Update the `SHEET_ID` constant with your actual Sheet ID:
   ```javascript
   const SHEET_ID = 'your_actual_sheet_id_here';
   ```
5. Save the project (Ctrl+S or Cmd+S)
6. Name your project (e.g., "DataGent Waitlist API")

### 3. Deploy the Google Apps Script

1. In the Apps Script editor, click "Deploy" → "New deployment"
2. Click the gear icon next to "Type" and select "Web app"
3. Configure the deployment:
   - **Description**: "DataGent Waitlist API v1"
   - **Execute as**: "Me"
   - **Who has access**: "Anyone"
4. Click "Deploy"
5. **Important**: Copy the Web app URL that appears
   - It will look like: `https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec`

### 4. Configure Your React App

1. Create a `.env` file in your project root (copy from `.env.example`)
2. Add your Google Apps Script URL:
   ```
   REACT_APP_GOOGLE_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
   ```
3. Restart your development server to load the new environment variable

### 5. Test the Integration

1. Fill out and submit the waitlist form on your website
2. Check your Google Sheet - you should see a new row with the form data
3. The sheet will automatically create headers on the first submission

## Data Structure

The Google Sheet will contain the following columns:

| Column | Description |
|--------|-------------|
| Timestamp | When the form was submitted (ISO format) |
| Email | User's email address |
| First Name | User's first name |
| Company | User's company (optional) |
| Role | User's role/position |
| Use Case | Primary use case for DataGent |
| Data Volume | Expected data volume |
| Source | How they found you (optional) |
| Consent | Whether they agreed to receive updates |
| Submission Date | Server timestamp when data was processed |

## Troubleshooting

### Common Issues

1. **"Script function not found" error**
   - Make sure you've deployed the script as a web app
   - Verify the deployment URL is correct

2. **"Permission denied" error**
   - Check that the script has access to Google Sheets
   - Verify the Sheet ID is correct
   - Make sure the deployment is set to "Anyone" access

3. **Data not appearing in sheet**
   - Check the browser console for error messages
   - Verify the environment variable is set correctly
   - Test the Google Apps Script directly using the `testScript()` function

4. **CORS errors**
   - This shouldn't happen with Google Apps Script, but if it does, make sure the deployment is configured correctly

### Testing the Google Apps Script

You can test the script directly in the Apps Script editor:

1. Open your Google Apps Script project
2. Select the `testScript` function from the dropdown
3. Click the "Run" button
4. Check your Google Sheet for a test entry

### Updating the Script

If you need to make changes to the Google Apps Script:

1. Edit the code in the Apps Script editor
2. Save the changes
3. Create a new deployment or update the existing one
4. The URL will remain the same for updates to existing deployments

## Security Considerations

- The Google Apps Script runs with your Google account permissions
- Only you can edit the script and access the sheet (unless you share it)
- The web app endpoint is public but only accepts the specific data format
- Consider adding rate limiting if you expect high traffic

## Alternative Approaches

If you prefer not to use Google Apps Script, you can also:

1. **Use Google Sheets API directly** (requires more setup and authentication)
2. **Use a service like Zapier or Make.com** (paid services but easier setup)
3. **Set up a backend server** (more control but requires infrastructure)

The current implementation provides a good balance of simplicity and functionality for most use cases.
