#!/usr/bin/env node

/**
 * Google Apps Script Configuration Setup
 * 
 * This script helps you configure your Google Apps Script
 * using environment variables from your .env file.
 * 
 * Usage:
 *   node scripts/setup-google-script.js
 *   npm run setup-google-script
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found. Please create one first.');
    process.exit(1);
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      envVars[key.trim()] = valueParts.join('=').trim();
    }
  });
  
  return envVars;
}

// Generate Google Apps Script configuration
function generateGoogleScriptConfig(envVars) {
  const requiredVars = [
    'REACT_APP_GOOGLE_SCRIPT_API_KEY',
    'REACT_APP_GOOGLE_SHEET_ID',
  ];
  
  const missing = requiredVars.filter(key => !envVars[key]);
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`   - ${key}`));
    process.exit(1);
  }
  
  // Parse allowed origins
  const allowedOrigins = [];
  
  // Add production domains
  if (envVars.REACT_APP_PRODUCTION_DOMAIN) {
    allowedOrigins.push(`https://${envVars.REACT_APP_PRODUCTION_DOMAIN}`);
    allowedOrigins.push(`https://www.${envVars.REACT_APP_PRODUCTION_DOMAIN}`);
  }
  
  // Add development domains if specified
  if (envVars.NODE_ENV !== 'production') {
    allowedOrigins.push('http://localhost:3000');
    allowedOrigins.push('http://localhost:5173');
    allowedOrigins.push('http://localhost:8080');
  }
  
  // Add custom origins
  if (envVars.REACT_APP_ALLOWED_ORIGINS) {
    const customOrigins = envVars.REACT_APP_ALLOWED_ORIGINS.split(',').map(o => o.trim());
    allowedOrigins.push(...customOrigins);
  }
  
  return {
    SHEET_ID: envVars.REACT_APP_GOOGLE_SHEET_ID,
    SHEET_NAME: envVars.REACT_APP_GOOGLE_SHEET_NAME || 'Waitlist',
    API_KEY: envVars.REACT_APP_GOOGLE_SCRIPT_API_KEY,
    ALLOWED_ORIGINS: allowedOrigins,
    MAX_REQUESTS_PER_MINUTE: envVars.REACT_APP_MAX_REQUESTS_PER_MINUTE || '10',
    MAX_REQUESTS_PER_HOUR: envVars.REACT_APP_MAX_REQUESTS_PER_HOUR || '100'
  };
}

// Generate the configuration code
function generateConfigurationCode(config) {
  return `
/**
 * Auto-generated configuration for Google Apps Script
 * Generated on: ${new Date().toISOString()}
 * 
 * Copy and paste this code into your Google Apps Script editor,
 * then run the updateConfiguration() function.
 */

function autoSetupConfiguration() {
  const config = ${JSON.stringify(config, null, 2)};
  
  return updateConfiguration(config);
}

// Run this function to apply the configuration
function applyAutoConfiguration() {
  console.log('Applying auto-generated configuration...');
  const result = autoSetupConfiguration();
  console.log('Configuration result:', result);
  return result;
}
`.trim();
}

// Main function
function main() {
  console.log('🔧 Google Apps Script Configuration Generator');
  console.log('=============================================\n');
  
  try {
    // Load environment variables
    console.log('📄 Loading environment variables...');
    const envVars = loadEnvFile();
    
    // Generate configuration
    console.log('⚙️  Generating configuration...');
    const config = generateGoogleScriptConfig(envVars);
    
    // Generate code
    const configCode = generateConfigurationCode(config);
    
    // Save to file
    const outputPath = path.join(process.cwd(), 'google-apps-script', 'auto-config.gs');
    fs.writeFileSync(outputPath, configCode);
    
    console.log('✅ Configuration generated successfully!\n');
    
    console.log('📋 Configuration Summary:');
    console.log(`   Sheet ID: ${config.SHEET_ID}`);
    console.log(`   Sheet Name: ${config.SHEET_NAME}`);
    console.log(`   API Key: ${config.API_KEY.substring(0, 8)}...`);
    console.log(`   Allowed Origins: ${config.ALLOWED_ORIGINS.length} domains`);
    config.ALLOWED_ORIGINS.forEach(origin => console.log(`     - ${origin}`));
    console.log(`   Rate Limits: ${config.MAX_REQUESTS_PER_MINUTE}/min, ${config.MAX_REQUESTS_PER_HOUR}/hour\n`);
    
    console.log('📝 Next Steps:');
    console.log('1. Open your Google Apps Script project');
    console.log('2. Copy the content from: google-apps-script/auto-config.gs');
    console.log('3. Paste it into your Google Apps Script editor');
    console.log('4. Run the applyAutoConfiguration() function');
    console.log('5. Check the execution log to confirm success');
    console.log('6. Test your waitlist form\n');
    
    console.log('🔗 File saved to: google-apps-script/auto-config.gs');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { loadEnvFile, generateGoogleScriptConfig, generateConfigurationCode };
