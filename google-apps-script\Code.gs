/**
 * Google Apps Script for DataGent Waitlist Integration
 * 
 * This script receives POST requests from your React app and writes
 * the waitlist data to a Google Sheet.
 * 
 * Setup Instructions:
 * 1. Create a new Google Sheet
 * 2. Note the Sheet ID from the URL
 * 3. Update the SHEET_ID constant below
 * 4. Deploy this script as a web app
 * 5. Set execution as "Anyone" and access as "Anyone, even anonymous"
 * 6. Copy the web app URL to your .env file as REACT_APP_GOOGLE_SCRIPT_URL
 */

// Configuration
const SHEET_ID = 'YOUR_GOOGLE_SHEET_ID_HERE'; // Replace with your actual Sheet ID
const SHEET_NAME = 'Waitlist'; // Name of the sheet tab

/**
 * Main function that handles POST requests
 */
function doPost(e) {
  try {
    // Parse the JSON data from the request
    const data = JSON.parse(e.postData.contents);
    
    // Validate required fields
    if (!data.email || !data.firstName) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Missing required fields: email and firstName'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Write to Google Sheet
    const result = writeToSheet(data);
    
    if (result.success) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: true,
          message: 'Data successfully added to waitlist',
          rowNumber: result.rowNumber
        }))
        .setMimeType(ContentService.MimeType.JSON);
    } else {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: result.error
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
  } catch (error) {
    console.error('Error in doPost:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Internal server error: ' + error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle GET requests (for testing)
 */
function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({
      message: 'DataGent Waitlist API is running',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Write waitlist data to Google Sheet
 */
function writeToSheet(data) {
  try {
    // Open the spreadsheet
    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    let sheet = spreadsheet.getSheetByName(SHEET_NAME);
    
    // Create the sheet if it doesn't exist
    if (!sheet) {
      sheet = spreadsheet.insertSheet(SHEET_NAME);
      
      // Add headers
      const headers = [
        'Timestamp',
        'Email',
        'First Name',
        'Company',
        'Role',
        'Use Case',
        'Data Volume',
        'Source',
        'Consent',
        'Submission Date'
      ];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // Format headers
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
    }
    
    // Prepare the row data
    const rowData = [
      data.timestamp || new Date().toISOString(),
      data.email || '',
      data.firstName || '',
      data.company || '',
      data.role || '',
      data.useCase || '',
      data.dataVolume || '',
      data.source || '',
      data.consent ? 'Yes' : 'No',
      new Date().toISOString()
    ];
    
    // Add the data to the next available row
    const lastRow = sheet.getLastRow();
    const newRow = lastRow + 1;
    sheet.getRange(newRow, 1, 1, rowData.length).setValues([rowData]);
    
    // Auto-resize columns for better readability
    sheet.autoResizeColumns(1, rowData.length);
    
    console.log('Successfully added data to row:', newRow);
    
    return {
      success: true,
      rowNumber: newRow
    };
    
  } catch (error) {
    console.error('Error writing to sheet:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * Test function to verify the script works
 * Run this function manually to test
 */
function testScript() {
  const testData = {
    email: '<EMAIL>',
    firstName: 'Test',
    company: 'Test Company',
    role: 'Founder / CXO',
    useCase: 'Sales & revenue analytics',
    dataVolume: '1–50 GB',
    source: 'website',
    consent: true,
    timestamp: new Date().toISOString()
  };
  
  const result = writeToSheet(testData);
  console.log('Test result:', result);
  
  return result;
}
