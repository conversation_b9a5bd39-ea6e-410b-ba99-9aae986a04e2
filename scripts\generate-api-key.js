#!/usr/bin/env node

/**
 * API Key Generator for Google Sheets Integration
 * 
 * This script generates a cryptographically secure API key
 * for use with the Google Apps Script authentication.
 * 
 * Usage:
 *   node scripts/generate-api-key.js
 *   npm run generate-api-key
 */

const crypto = require('crypto');

function generateApiKey(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

function generateSecurePassword(length = 24) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

console.log('🔐 Google Sheets API Key Generator');
console.log('=====================================\n');

console.log('Option 1 - Base64 Encoded (Recommended):');
console.log(`REACT_APP_GOOGLE_SCRIPT_API_KEY=${generateApiKey()}\n`);

console.log('Option 2 - Alphanumeric with Symbols:');
console.log(`REACT_APP_GOOGLE_SCRIPT_API_KEY=${generateSecurePassword()}\n`);

console.log('Option 3 - Longer Base64 (Extra Secure):');
console.log(`REACT_APP_GOOGLE_SCRIPT_API_KEY=${generateApiKey(48)}\n`);

console.log('📝 Instructions:');
console.log('1. Copy one of the keys above');
console.log('2. Add it to your .env file');
console.log('3. Update the API_KEY constant in your Google Apps Script');
console.log('4. Redeploy your Google Apps Script');
console.log('5. Test the integration\n');

console.log('⚠️  Security Notes:');
console.log('- Never commit API keys to version control');
console.log('- Use different keys for development and production');
console.log('- Rotate keys periodically for better security');
console.log('- Keep keys confidential and secure');
