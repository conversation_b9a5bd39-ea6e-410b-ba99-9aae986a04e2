# Google Sheets Integration
# URL of your Google Apps Script web app for handling waitlist submissions
# Example: https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
REACT_APP_GOOGLE_SCRIPT_URL=

# API key for Google Apps Script authentication (generate a random string)
# Example: your-secret-api-key-here-make-it-long-and-random
REACT_APP_GOOGLE_SCRIPT_API_KEY=

# Optional: Add other environment variables as needed
# REACT_APP_API_BASE_URL=
# REACT_APP_ANALYTICS_ID=
