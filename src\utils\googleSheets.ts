/**
 * Google Sheets integration utilities
 * 
 * This module handles sending waitlist form data to Google Sheets
 * using Google Apps Script as a web app endpoint.
 */

export interface WaitlistSubmission {
  email: string;
  firstName: string;
  company?: string;
  role: string;
  useCase: string;
  dataVolume?: string;
  source?: string;
  consent: boolean;
  timestamp: string;
}

/**
 * Submit waitlist data to Google Sheets via Google Apps Script
 * 
 * @param data - The waitlist form data to submit
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export const submitToGoogleSheets = async (data: WaitlistSubmission): Promise<boolean> => {
  const GOOGLE_SCRIPT_URL = process.env.REACT_APP_GOOGLE_SCRIPT_URL;
  
  if (!GOOGLE_SCRIPT_URL) {
    console.warn('Google Script URL not configured in environment variables');
    return false;
  }

  try {
    const response = await fetch(GOOGLE_SCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Check if the Google Apps Script returned a success indicator
    if (result.success === false) {
      throw new Error(result.error || 'Google Apps Script returned failure');
    }

    console.log('Successfully submitted to Google Sheets:', result);
    return true;
    
  } catch (error) {
    console.error('Failed to submit to Google Sheets:', error);
    return false;
  }
};

/**
 * Fallback function to store data locally when Google Sheets is unavailable
 * 
 * @param data - The waitlist form data to store locally
 */
export const storeLocally = (data: WaitlistSubmission): void => {
  try {
    const existingData = localStorage.getItem('detailedWaitlist') || '[]';
    const waitlist = JSON.parse(existingData);
    waitlist.push(data);
    localStorage.setItem('detailedWaitlist', JSON.stringify(waitlist));
    console.log('Data stored locally as fallback');
  } catch (error) {
    console.error('Failed to store data locally:', error);
  }
};

/**
 * Main function to handle waitlist submission with fallback
 * 
 * @param data - The waitlist form data
 * @returns Promise<{ success: boolean, method: 'sheets' | 'local' }>
 */
export const handleWaitlistSubmission = async (data: Omit<WaitlistSubmission, 'timestamp'>): Promise<{ success: boolean, method: 'sheets' | 'local' }> => {
  const submissionData: WaitlistSubmission = {
    ...data,
    timestamp: new Date().toISOString(),
  };

  // Try Google Sheets first
  const sheetsSuccess = await submitToGoogleSheets(submissionData);
  
  if (sheetsSuccess) {
    return { success: true, method: 'sheets' };
  }

  // Fallback to local storage
  storeLocally(submissionData);
  return { success: true, method: 'local' };
};
